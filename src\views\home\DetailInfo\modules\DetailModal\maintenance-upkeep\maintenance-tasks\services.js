import { request } from '@/utils/request'

// 维养任务-列表分页查询
export function getTaskPage(data) {
  return request({
    url: '/prjstd/task/page',
    method: 'post',
    data
  })
}
// 增加
export function addTask(data) {
  return request({
    url: '/prjstd/task/add',
    method: 'post',
    data
  })
}
// 详情
export function getTaskById(params) {
  return request({
    url: '/prjstd/task/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 更新
export function editTask(data) {
  return request({
    url: '/prjstd/task/update',
    method: 'post',
    data
  })
}
// 删除
export function deleteTask(params) {
  return request({
    url: '/prjstd/task/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
