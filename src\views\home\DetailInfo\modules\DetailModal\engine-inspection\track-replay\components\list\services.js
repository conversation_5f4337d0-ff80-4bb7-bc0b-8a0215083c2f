import {request} from '@/utils/request'

// 巡检任务——列表分页查询
export function getPatrolTaskPage(data) {
  return request({
    url: '/patrol/task/page',
    method: 'post',
    data,
  })
}

// 巡检任务——删除
export function deletePatrolTask(params) {
  return request({
    url: '/patrol/task/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 巡检任务-新增
export function addPatrolTask(data) {
  return request({
    url: '/patrol/task/add',
    method: 'post',
    data,
  })
}
// 巡检任务-更新
export function updatePatrolTask(data) {
  return request({
    url: '/patrol/task/update',
    method: 'post',
    data,
  })
}
// 巡检任务——详情 /patrol/task/runDetails
export function getPatrolTask(params) {
  return request({
    url: '/patrol/task/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 监控任务详情
export function getRunDetails(params) {
  return request({
    url: '/patrol/task/runDetails',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 巡检任务——任务作废
export function cancelPatrolTask(params) {
  return request({
    url: '/patrol/task/cancel',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 巡检任务——复制
export function copyPatrolTask(params) {
  return request({
    url: '/patrol/task/copyById',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 左侧列表
// 巡检线路——未选择巡检对象列表
export function chooseObjectPatrolLine(params) {
  return request({
    url: '/patrol/line/chooseObject',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 巡检线路——选择巡检对象巡检项列表
export function getPatrolObjectList(data) {
  return request({
    url: '/patrol/task/object/page',
    method: 'post',
    data,
  })
}

// 巡检计划——班组列表
export function getWorkGroupList(params) {
  return request({
    url: '/work/group/groupList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 巡检计划——班组人员列表
export function getWorkGroupUserList(data) {
  return request({
    url: '/work/group/groupUserList',
    method: 'post',
    data,
  })
}

// 巡检线路——内容配置详情
export function getConfigPatrolLine(params) {
  return request({
    url: '/patrol/line/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//我的任务详情
export function getPatrolTaskProfile(taskId) {
  return request({
    url: '/patrol/task/profile',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { taskId: taskId },
  })
}
//我的任务详情
export function getMyTaskObjectList(taskId) {
  return request({
    url: '/patrol/task/myTask/object/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { taskId: taskId },
  })
}
//
export function getTaskContentDetails(params) {
  return request({
    url: '/patrol/task/content/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 导出巡检报告-单个
export function exportById(params) {
  return request({
    url: '/patrol/task/report/exportById',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 导出巡检报告 - 批量
export function exportTask(data) {
  return request({
    url: '/patrol/task/report/export',
    method: 'post',
    data,
  })
}
