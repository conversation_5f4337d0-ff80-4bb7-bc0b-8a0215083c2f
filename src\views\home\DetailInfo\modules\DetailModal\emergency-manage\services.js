import { request } from '@/utils/request'

// 应急响应_应急预案-列表分页查询
export function getPlanPage(data) {
  return request({
    url: '/prjstd/plan/page',
    method: 'post',
    data
  })
}
// 增加
export function addPlan(data) {
  return request({
    url: '/prjstd/plan/add',
    method: 'post',
    data
  })
}
// 详情
export function getPlanById(params) {
  return request({
    url: '/prjstd/plan/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 更新
export function editPlan(data) {
  return request({
    url: '/prjstd/plan/update',
    method: 'post',
    data
  })
}
// 删除
export function deletePlan(params) {
  return request({
    url: '/prjstd/plan/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 应急响应_放水预警-列表分页查询
export function getReleasePage(data) {
  return request({
    url: '/prjstd/release/page',
    method: 'post',
    data
  })
}
// 增加
export function addRelease(data) {
  return request({
    url: '/prjstd/release/add',
    method: 'post',
    data
  })
}
// 详情
export function getReleaseById(params) {
  return request({
    url: '/prjstd/release/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 更新
export function editRelease(data) {
  return request({
    url: '/prjstd/release/update',
    method: 'post',
    data
  })
}
// 删除
export function deleteRelease(params) {
  return request({
    url: '/prjstd/release/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 应急响应_安全应急预警-列表分页查询
export function getWarningPage(data) {
  return request({
    url: '/prjstd/warning/page',
    method: 'post',
    data
  })
}
// 增加
export function addWarning(data) {
  return request({
    url: '/prjstd/warning/add',
    method: 'post',
    data
  })
}
// 详情
export function getWarningById(params) {
  return request({
    url: '/prjstd/warning/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
// 更新
export function editWarning(data) {
  return request({
    url: '/prjstd/warning/update',
    method: 'post',
    data
  })
}
// 删除
export function deleteWarning(params) {
  return request({
    url: '/prjstd/warning/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}
