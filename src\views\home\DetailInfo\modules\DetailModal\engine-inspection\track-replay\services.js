import {request} from '@/utils/request'

// 轨迹回放列表
export function getPatrolTaskList(data) {
  return request({
    url: '/patrol/task/prjstd/track/list',
    method: 'post',
    data
  })
}

// 巡检人点位详情
export function getTaskTrack(params) {
  return request({
    url: '/patrol/task/track/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 巡检计划——线路列表
export function getPatrolLineList(data) {
  return request({
    url: '/patrol/line/page',
    method: 'post',
    data
  })
}

// 巡检计划——班次列表
export function getWorkShiftList(data) {
  return request({
    url: '/work/shift/page',
    method: 'post',
    data
  })
}

// 巡检线路——选择巡检对象巡检项列表
export function getPatrolObjectList(data) {
  return request({
    url: '/patrol/task/object/page',
    method: 'post',
    data,
  })
}

// 巡检线路——分页列表
export function getPatrolLinePage(data) {
  return request({
    url: '/patrol/line/page',
    method: 'post',
    data
  })
}

// 巡检线路——新增
export function addPatrolLine(data) {
  return request({
    url: '/patrol/line/add',
    method: 'post',
    data
  })
}

// 巡检线路——删除
export function deletePatrolLine(params) {
  return request({
    url: '/patrol/line/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 巡检线路——更新
export function updatePatrolLine(data) {
  return request({
    url: '/patrol/line/update',
    method: 'post',
    data
  })
}

// 巡检对象——对象分页列表
export function getPatrolObjectPage(data) {
  return request({
    url: '/patrol/object/page',
    method: 'post',
    data
  })
}

// 巡检线路——选择巡检对象
export function getChooseObject(params) {
  return request({
    url: '/patrol/line/chooseObject',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 巡检线路——内容配置详情
export function getLine(params) {
  return request({
    url: '/patrol/line/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 巡检线路——内容配置
export function patrolLineConfig(data) {
  return request({
    url: '/patrol/line/config',
    method: 'post',
    data
  })
}
