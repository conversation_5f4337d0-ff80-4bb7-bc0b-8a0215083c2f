import { request } from '@/utils/request'

// 工程下的岗位类别
export function getPositionType(params) {
  return request({
    url: '/prjstd/position/getPositionType',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 岗位类别下的岗位信息
export function getPositionList(params) {
  return request({
    url: '/prjstd/position/getPositionList',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 人员信息-详情
export function getPersonnel(params) {
  return request({
    url: '/prjstd/personnel/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 人员信息-列表分页查询
export function getPersonnelPage(data) {
  return request({
    url: '/prjstd/personnel/page',
    method: 'post',
    data
  })
}

// 岗位信息-列表分页查询
export function getPositionPage(data) {
  return request({
    url: '/prjstd/position/page',
    method: 'post',
    data
  })
}

// 年度培训统计
export function getYearTrainingList(params) {
  return request({
    url: '/prjstd/training/getYearTrainingList',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 月度培训数量趋势分析
export function getMonthTrainingList(params) {
  return request({
    url: '/prjstd/training/getMonthTrainingList',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 年度培训明细
export function getTrainingList(params) {
  return request({
    url: '/prjstd/training/getTrainingList',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

// 详情
export function getTraining(params) {
  return request({
    url: '/prjstd/training/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    params
  })
}

// 列表分页查询
export function getUnitPage(data) {
  return request({
    url: '/prjstd/unit/page',
    method: 'post',
    data
  })
}
