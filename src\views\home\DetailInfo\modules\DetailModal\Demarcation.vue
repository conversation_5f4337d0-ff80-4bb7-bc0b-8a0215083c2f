<template>
  <div class="demarcation">
    <MapBox :options="{}" @onMapMounted="onMapMounted" />

    <div style="position: absolute; right: 16px; bottom: 16px">
      <MapStyle v-if="!!mapIns" :mapIns="mapIns" ref="mapStyleRef" />
    </div>

    <div class="buttons">
      <a-button
        type="primary"
        :disabled="list && list.some(el => el.projectAttachName == '管理范围线')"
        style="margin-right: 10px"
        @click="() => onUploadClick('管理范围线', 'lineRangeAttach')"
      >
        上传管理范围线
      </a-button>
      <a-button
        type="primary"
        :disabled="list && list.some(el => el.projectAttachName == '保护范围线')"
        @click="() => onUploadClick('保护范围线', 'protectionLineAttach')"
      >
        上传保护范围线
      </a-button>
    </div>
    <AddFileModal
      title="上传范围线"
      v-if="showAddModal"
      :readeOnlyName="readeOnlyName"
      ref="AddFileModalRef"
      @confirm="onAddFileModalConfirm"
    />
  </div>
</template>
<script lang="jsx">
  // import { getProjectAttachList, addProjectAttach } from '../../services'
  import { getProjectAttachList, addProjectAttach, deleteProjectAttach } from '../../services'
  import { getDemarcation } from '../../services'
  import MapBox from '@/components/MapBox'
  import AddFileModal from './components/AddFileModal.vue'
  import axios from 'axios'
  import * as turf from '@turf/turf'
  import MapStyle from '@/components/MapBox/MapStyle.vue'

  export default {
    name: 'Demarcation',
    props: {},
    components: { MapBox, AddFileModal, MapStyle },
    data() {
      return {
        showAddModal: false,
        readeOnlyName: '管理范围线',
        list: null,
        mapIns: null,
        displayCode: '',
      }
    },
    computed: {},

    created() {},
    methods: {
      onMapMounted(mapIns) {
        this.mapIns = mapIns
        this.getList()
      },
      getList() {
        getDemarcation({
          projectId: this.$attrs.projectId,
        }).then(res => {
          const arr = Object.keys(res.data || {})
            .filter(el => !!res.data[el])
            .map(el => res.data[el])
          Promise.all(arr.map(el => axios.get(el.attachUrl))).then(resp => {
            resp.forEach((item, idx) => {
              arr[idx].layerData = resp[idx].data
            })
            this.list = arr
            this.addLayers()
          })
        })
      },

      addLayers() {
        if (this.mapIns && this.list.length) {
          const allPoint = []
          this.list.forEach(ele => {
            // map偏移处理
            turf.geomEach(ele.layerData, currentSegment => {
              // 获取点位
              const explode = turf.explode(currentSegment)
              allPoint.push.apply(allPoint, explode.features)
            })

            if (this.mapIns.getLayer(`layer${ele.projectAttachId}`)) {
            } else {
              this.mapIns.addSource(`layer${ele.projectAttachId}`, {
                type: 'geojson',
                data: ele.layerData,
              })
              this.mapIns.addLayer({
                //绘制边
                id: `layer${ele.projectAttachId}`,
                type: 'line',
                source: `layer${ele.projectAttachId}`,
                layout: {
                  visibility: 'visible',
                },
                paint: {
                  'line-color': ele.projectAttachName === '管理范围线' ? '#95F204' : '#D9001B',
                  'line-width': 2,
                },
              })

              // this.mapIns.addLayer({
              //   id: `layer${ele.projectAttachId}`,
              //   type: 'symbol',
              //   source: `layer${ele.projectAttachId}`,
              //   layout: {
              //     'text-size': 12,
              //     'text-field': ['get', 'lineName'],
              //     // 'text-offset': [0, 1.25],
              //     'text-anchor': 'center'
              //   },
              //   filter: ['==', '$type', 'Point']
              // })
            }
          })

          // map偏移处理
          const enveloped = turf.envelope(turf.featureCollection(allPoint))
          this.mapIns.fitBounds(
            [
              [enveloped.bbox[0], enveloped.bbox[1]],
              [enveloped.bbox[2], enveloped.bbox[3]],
            ],
            {
              animate: true,
              padding: { top: 50, bottom: 50, left: 50, right: 50 },
            },
          )
        }
      },

      onFullScreen() {
        this.mapIns.resize()
      },
      onUploadClick(readeOnlyName, displayCode) {
        this.showAddModal = true
        this.readeOnlyName = readeOnlyName

        this.displayCode = displayCode
        this.$nextTick(() => this.$refs.AddFileModalRef.showModal())
      },
      onAddFileModalConfirm(params, callback) {
        addProjectAttach({
          projectAttachName: params.name,
          attachUrl: params.url,
          projectId: this.$attrs.projectId,
          displayCode: this.displayCode,
        }).then(res => {
          this.getList()
          callback()
          this.showAddModal = false
        })
      },
    },
  }
</script>

<style lang="scss" scoped>
  .demarcation {
    width: 100%;
    height: 100%;
    position: relative;
    .buttons {
      position: absolute;
      right: 10px;
      top: 10px;
    }
  }
</style>
